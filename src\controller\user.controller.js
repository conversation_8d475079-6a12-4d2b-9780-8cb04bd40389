const { sql, query, queryWithParams } = require('../service/index.js')
const http = require('http')
const { WX_APPSECRET, WX_APPID } = process.env

class UserController {
  async WXLogin(ctx) {
    const { code } = ctx.request.body
    const url = `https://api.weixin.qq.com/sns/jscode2session?appid=${WX_APPID}&secret=${WX_APPSECRET}&js_code=${code}&grant_type=authorization_code`
    const result = await http.get(url)
    ctx.body = { code: 200, data: result }
  }
}

module.exports = new UserController()
